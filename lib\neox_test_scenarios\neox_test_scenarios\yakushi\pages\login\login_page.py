#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Login Page Object

This module provides the Page Object implementation for Yakushi login functionality.
It encapsulates all login-related UI elements and operations.

@Date       :   2024-02-19
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   login_page.py
@Software   :   PyCharm
"""

import random
import string
import subprocess
import time
from typing import Any, Dict, Optional

import allure
import uiautomation as ui
from neox_test_common import UIA, logger

from ..base.base_page import BasePage


class LoginPage(BasePage):
    """
    Yakushi登录页面的Page Object类

    该类封装了登录页面的所有UI元素和操作，包括用户名输入、密码输入、
    登录按钮点击等功能。提供了完整的登录流程操作方法。

    继承自BasePage，具有基础页面的所有通用功能。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化登录页面

        Args:
            config (dict): 测试配置数据，包含yakushi.toml的配置信息
        """
        super().__init__(config)

    def get_username_textbox(self) -> Optional[ui.EditControl]:
        """
        获取用户名输入框控件

        Returns:
            EditControl: 用户名输入框控件，如果未找到则返回None
        """
        return self.find_element_by_config(
            "yakushi.modules.login.control.box.user", element_type="Edit"
        )

    def get_password_textbox(self) -> Optional[ui.EditControl]:
        """
        获取密码输入框控件

        Returns:
            EditControl: 密码输入框控件，如果未找到则返回None
        """
        return self.find_element_by_config(
            "yakushi.modules.login.control.box.password", element_type="Edit"
        )

    def get_login_button(self) -> Optional[ui.ButtonControl]:
        """
        获取登录按钮控件

        Returns:
            ButtonControl: 登录按钮控件，如果未找到则返回None
        """
        return self.find_element_by_config(
            "yakushi.modules.login.control.btn.login", element_type="Button"
        )

    def input_username(self, username: str) -> bool:
        """
        在用户名输入框中输入用户名

        Args:
            username (str): 要输入的用户名

        Returns:
            bool: 操作是否成功
        """
        try:
            login_window = self.find_window()
            if not login_window:
                logger.error("Login window not found")
                return False

            self.activate_window(login_window)

            # 使用带重试机制的元素查找
            username_box = self.find_element_with_retry(
                "yakushi.modules.login.control.box.user", element_type="Edit"
            )

            # 添加重试机制的输入操作
            for attempt in range(3):
                try:
                    # Clear existing text first
                    UIA.inputEditControlText(editControl=username_box, text="")
                    # Input new username
                    success = UIA.inputEditControlText(
                        editControl=username_box, text=username
                    )

                    if success:
                        logger.info("Successfully input username")
                        self.log_element_info(username_box, "用户名输入框")
                        return True
                    else:
                        logger.warning(
                            f"Failed to input username, attempt {attempt + 1}"
                        )

                except Exception as e:
                    logger.warning(f"输入用户名失败，第{attempt + 1}次重试：{e}")
                    time.sleep(1)

            logger.error("Failed to input username after 3 attempts")
            return False

        except Exception as e:
            logger.error(f"Error inputting username: {e}")
            return False

    def input_password(self, password: str) -> bool:
        """
        在密码输入框中输入密码

        Args:
            password (str): 要输入的密码

        Returns:
            bool: 操作是否成功
        """
        try:
            login_window = self.find_window()
            if not login_window:
                logger.error("Login window not found")
                return False

            self.activate_window(login_window)

            # 使用带重试机制的元素查找
            password_box = self.find_element_with_retry(
                "yakushi.modules.login.control.box.password", element_type="Edit"
            )

            # 添加重试机制的输入操作
            for attempt in range(3):
                try:
                    # Clear existing text first
                    UIA.inputEditControlText(editControl=password_box, text="")
                    # Input new password
                    success = UIA.inputEditControlText(
                        editControl=password_box, text=password
                    )

                    if success:
                        logger.info("Successfully input password")
                        self.log_element_info(password_box, "密码输入框")
                        return True
                    else:
                        logger.warning(
                            f"Failed to input password, attempt {attempt + 1}"
                        )

                except Exception as e:
                    logger.warning(f"输入密码失败，第{attempt + 1}次重试：{e}")
                    time.sleep(1)

            logger.error("Failed to input password after 3 attempts")
            return False

        except Exception as e:
            logger.error(f"Error inputting password: {e}")
            return False

    def click_login_button(self) -> bool:
        """
        点击登录按钮

        Returns:
            bool: 操作是否成功
        """
        try:
            login_window = self.find_window()
            if not login_window:
                logger.error("Login window not found")
                return False

            self.activate_window(login_window)

            # 使用带重试机制的元素查找
            login_button = self.find_element_with_retry(
                "yakushi.modules.login.control.btn.login", element_type="Button"
            )

            # 添加重试机制的点击操作
            for attempt in range(3):
                try:
                    success = UIA.clickButton(button=login_button)

                    if success:
                        logger.info("Successfully clicked login button")
                        self.log_element_info(login_button, "登录按钮")
                        return True
                    else:
                        logger.warning(
                            f"Failed to click login button, attempt {attempt + 1}"
                        )

                except Exception as e:
                    logger.warning(f"点击登录按钮失败，第{attempt + 1}次重试：{e}")
                    time.sleep(1)

            logger.error("Failed to click login button after 3 attempts")
            return False

        except Exception as e:
            logger.error(f"Error clicking login button: {e}")
            return False

    def open_yakushi_app(self) -> Optional[ui.WindowControl]:
        """
        打开Yakushi客户端应用程序

        Returns:
            WindowControl: 登录窗口控件，如果打开失败则返回None
        """
        # First check if Yakushi client is already running
        login_window = self.find_window(timeout=1)
        if login_window:
            logger.info("Yakushi client is already running")
            return login_window

        # Get executable path from config
        exe_path = self.get_element_config("yakushi.locate.exe_abs_path")
        if not exe_path:
            logger.error("Yakushi executable path not found in config")
            return None

        try:
            logger.info(f"Starting Yakushi client from: {exe_path}")
            subprocess.Popen(exe_path)

            # Wait for login window to appear
            timeout = (
                self.get_element_config(
                    "yakushi.modules.login.check_cert_installed_timeout"
                )
                or 20
            )

            for attempt in range(timeout):
                login_window = self.find_window(timeout=1)
                if login_window:
                    logger.info(
                        f"Yakushi client started successfully (attempt {attempt + 1})"
                    )
                    return login_window
                time.sleep(1)

            logger.error(f"Yakushi client failed to start within {timeout} seconds")
            return None

        except Exception as e:
            logger.error(f"Error starting Yakushi client: {e}")
            return None

    def wait_for_login_result(self, timeout: int = 10) -> bool:
        """
        等待登录结果

        Args:
            timeout (int): 等待超时时间（秒）

        Returns:
            bool: True表示登录成功，False表示登录失败或超时
        """
        # Implementation will be added based on specific login result detection logic
        # This is a placeholder for now
        time.sleep(2)  # Basic wait for login processing
        return True

    def perform_complete_login_flow(self, use_correct_credentials: bool = True) -> None:
        """
        执行完整的登录流程

        Args:
            use_correct_credentials (bool): True使用正确凭据，False使用错误凭据
        """
        # Step 1: Show desktop
        with allure.step("显示桌面"):
            UIA.showDesktop()

        # Step 2: Open Yakushi client
        with allure.step("打开Yakushi客户端"):
            window = self.open_yakushi_app()
            assert window is not None, "Failed to open Yakushi client"

        # Step 3: Input username
        if use_correct_credentials:
            username = self.get_element_config("yakushi.modules.login.account")
            step_desc = "在账户信息文本框中输入正确的账户名"
        else:
            username = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(8)
            )
            step_desc = "在账户信息文本框中输入错误的账户名"

        with allure.step(step_desc):
            success = self.input_username(username)
            assert success, f"Failed to input username: {username}"

        # Step 4: Input password
        if use_correct_credentials:
            password = self.get_element_config("yakushi.modules.login.password")
            step_desc = "在密码文本框中输入正确的密码"
        else:
            password = "".join(
                random.choice(string.ascii_letters + string.digits) for _ in range(8)
            )
            step_desc = "在密码文本框中输入错误的密码"

        with allure.step(step_desc):
            success = self.input_password(password)
            assert success, "Failed to input password"

        # Step 5: Click login button
        with allure.step("点击登录按钮"):
            success = self.click_login_button()
            assert success, "Failed to click login button"

    def verify_login_success(self) -> None:
        """
        验证登录成功

        检查是否成功跳转到主页面，验证登录操作是否成功完成
        """
        with allure.step("验证登录成功"):
            # Wait for potential login processing
            time.sleep(3)

            # Check for main window or float window
            main_window_auto_id = self.get_element_config(
                "yakushi.modules.common.window.main.auto_id"
            )
            float_window_auto_id = self.get_element_config(
                "yakushi.modules.common.window.float.auto_id"
            )

            main_window = self.find_window(auto_id=main_window_auto_id, timeout=5)
            float_window = self.find_window(auto_id=float_window_auto_id, timeout=5)

            if main_window:
                logger.info("Login successful - Main window found")
                self.log_element_info(main_window, "主窗口")
            elif float_window:
                logger.info("Login successful - Float window found")
                self.log_element_info(float_window, "浮动窗口")
            else:
                raise AssertionError(
                    "Login failed - Neither main window nor float window found"
                )

    def verify_login_failure(self) -> None:
        """
        验证登录失败

        检查登录失败后的状态，确认仍然停留在登录页面
        """
        with allure.step("验证登录失败"):
            # Wait for potential error message processing
            time.sleep(2)

            # Check if still on login window
            login_window = self.find_window(timeout=3)

            if login_window:
                logger.info("Login failed as expected - Still on login window")
                self.log_element_info(login_window, "登录窗口")
            else:
                raise AssertionError(
                    "Expected login failure but login window not found"
                )
